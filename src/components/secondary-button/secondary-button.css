.secondary-button {
  align-items: center;
  background-color: var(--color-support);
  border: none;
  border-radius: var(--radius-m);
  color: var(--color-content-accent);
  cursor: pointer;
  display: inline-flex;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  gap: 8px;
  height: 32px;
  justify-content: center;
  padding: 6px 12px;
  position: relative;
  transition: background-color 0.2s ease, opacity 0.2s ease;
  white-space: nowrap;
}

/* 图标样式 */
.secondary-button__icon {
  height: 16px;
  width: 16px;
}

/* 悬停状态 */
.secondary-button:hover:not(:disabled) {
  background-color: var(--color-support-hover);
}

/* 禁用状态 */
.secondary-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 激活状态 */
.secondary-button:active:not(:disabled) {
  background-color: var(--color-support-hover);
  transform: translateY(1px);
}

/* 满宽模式 */
.secondary-button--full-width {
  width: 100%;
}
