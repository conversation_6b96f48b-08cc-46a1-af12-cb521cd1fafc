import React from "react";
import type { LucideIcon } from "lucide-react";
import { Plus } from "lucide-react";
import "./secondary-button.css";

interface SecondaryButtonProps {
  /** 是否显示图标，默认 true */
  showIcon?: boolean;
  /** 按钮文本内容 */
  children: React.ReactNode;
  /** 按钮前置图标，默认为 Plus */
  icon?: LucideIcon;
  /** 自定义类名 */
  className?: string;
  /** 按钮禁用状态 */
  disabled?: boolean;
  /** 点击事件处理函数 */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** 按钮类型 */
  type?: "button" | "submit" | "reset";
  /** 按钮尺寸，默认为 'medium' */
  size?: "small" | "medium" | "large";
  /** 满宽模式（占满父容器宽度） */
  fullWidth?: boolean;
}

export const SecondaryButton: React.FC<SecondaryButtonProps> = ({
  children,
  icon: Icon = Plus,
  className = "",
  disabled = false,
  onClick,
  type = "button",
  size = "medium",
  fullWidth = false,
  showIcon = true,
}: SecondaryButtonProps) => {
  return (
    <button
      className={`secondary-button secondary-button--${size} ${fullWidth ? "secondary-button--full-width" : ""} ${className}`}
      disabled={disabled}
      onClick={onClick}
      type={type}
    >
      {showIcon && Icon && <Icon className="secondary-button__icon" />}
      <span className="secondary-button__text">{children}</span>
    </button>
  );
};
