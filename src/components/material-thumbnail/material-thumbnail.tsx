import React, { memo, useCallback, useRef } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment } from '@react-three/drei';
import * as THREE from 'three';
import type { MaterialData } from '../../services/api';
import './material-thumbnail.css';

interface MaterialThumbnailProps {
  material: MaterialData;
  active: boolean;
  onClick?: () => void;
}

/**
 * 材质小缩略图（3D球体）
 * 为了性能，Canvas 使用 frameloop="demand"，首次渲染后停止自动渲染。
 */
const MaterialThumbnail: React.FC<MaterialThumbnailProps> = ({ material, active, onClick }) => {
  const invalidateRef = useRef<(() => void) | null>(null);

  const handleCreated = useCallback(({ invalidate }: { invalidate: () => void }) => {
    // 保存 invalidate，在 Sphere 材质准备好后调用一次使其渲染首帧
    invalidateRef.current = invalidate;
  }, []);

  return (
    <div
      className={`material-item${active ? ' active' : ''}`}
      onClick={onClick}
    >
      <div className="thumbnail-canvas">
      <Canvas
        frameloop="demand"
        camera={{ position: [0, 0, 1.5], fov: 45 }}
        onCreated={handleCreated}
        gl={{ antialias: true, alpha: true }}
      >
        {/* 环境光/主光源 */}
        <ambientLight intensity={0.7} />
        <directionalLight position={[5, 5, 5]} intensity={1} />

        {/* 材质球 */}
        <mesh
          onUpdate={() => {
            // 渲染一次首帧
            invalidateRef.current?.();
          }}
        >
          <sphereGeometry args={[1, 64, 64]} />
          <meshStandardMaterial
            color={new THREE.Color(material.color)}
            metalness={material.metalness / 100}
            roughness={material.roughness / 100}
            transparent={material.glass > 0}
            opacity={material.glass > 0 ? (100 - material.glass) / 100 : 1}
          />
        </mesh>

        {/* 使用城市 HDR 环境 */}
        <Environment preset="city" />
      </Canvas>
      </div>
    </div>
  );
};

export default memo(MaterialThumbnail);
