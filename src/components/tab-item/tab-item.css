/* 标签项基础样式 */
.tab-item {
  height: 28px;
  padding: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 6px;
  cursor: pointer;
  user-select: none;
  box-sizing: border-box;
}

/* 默认状态 */
.tab-item--default {
  background: transparent;
}

/* 激活状态 */
.tab-item--active {
  background: var(--color-support, rgba(255, 255, 255, 0.12));
}

/* 文本样式 */
.tab-item__text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  word-wrap: break-word;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 默认状态文本颜色 */
.tab-item__text--default {
  color: var(--color-content-regular, rgba(255, 255, 255, 0.70));
}

/* 激活状态文本颜色 */
.tab-item__text--active {
  color: var(--color-content-accent);
}

/* 图标容器 */
.tab-item__icon-container {
  width: 16px;
  height: 16px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 默认状态图标颜色 */
.tab-item__icon--default svg {
  stroke: var(--color-content-regular);
  stroke-width: 1px;
}

/* 激活状态图标颜色 */
.tab-item__icon--active svg {
  stroke: var(--color-content-accent);
  stroke-width: 1px;
}

/* 适配浅色主题 */
.theme-light .tab-item--active {
  background: var(--color-support);
}

