import React, { useState } from 'react';
import { Pipette, Plus } from 'lucide-react';
import { HexColorPicker } from 'react-colorful';
import './custom-material-panel.css';
import { Slider } from '../slider/slider';

interface CustomMaterialPanelProps {
  onChange?: (material: MaterialSettings) => void;
  defaultSettings?: MaterialSettings;
}

export interface MaterialSettings {
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  textureUrl?: string;
  // 斑点强度 0-1
  speckleIntensity?: number;
  // 变色强度 0-1，对应 iridescence
  iridescence?: number;
  // 纹理缩放 0.1-5
  textureScale?: number;
  // 纹理旋转 0-360°
  textureRotation?: number;
}

export const CustomMaterialPanel: React.FC<CustomMaterialPanelProps> = ({ 
  onChange, 
  defaultSettings
}) => {
  // 设置默认值
  const defaultColor = defaultSettings?.color || '#B39B9C';
  const defaultMetalness = defaultSettings?.metalness ?? 0.5;
  const defaultRoughness = defaultSettings?.roughness ?? 0.5;
  const defaultOpacity = defaultSettings?.opacity ?? 1;
  const defaultSpeckle = defaultSettings?.speckleIntensity ?? 0;
  const defaultIridescence = defaultSettings?.iridescence ?? 0;
  const defaultTexScale = defaultSettings?.textureScale ?? 1;
  const defaultTexRot = defaultSettings?.textureRotation ?? 0;

  // 状态
  const [color, setColor] = useState<string>(defaultColor);
  const [metalness, setMetalness] = useState<number>(defaultMetalness);
  const [roughness, setRoughness] = useState<number>(defaultRoughness);
  const [opacity, setOpacity] = useState<number>(defaultOpacity);
  const [textureUrl, setTextureUrl] = useState<string | undefined>(defaultSettings?.textureUrl);
  const [speckle, setSpeckle] = useState<number>(defaultSpeckle);
  const [iridescence, setIridescence] = useState<number>(defaultIridescence);
  const [texScale, setTexScale] = useState<number>(defaultTexScale);
  const [texRot, setTexRot] = useState<number>(defaultTexRot);

  // 更新材质设置
  const updateSettings = (
    newColor?: string, 
    newMetalness?: number, 
    newRoughness?: number, 
    newOpacity?: number,
    newTextureUrl?: string,
    newSpeckle?: number,
    newIrides?: number,
    newTexScale?: number,
    newTexRot?: number,
  ) => {
    const updatedSettings: MaterialSettings = {
      color: newColor ?? color,
      metalness: newMetalness ?? metalness,
      roughness: newRoughness ?? roughness,
      opacity: newOpacity ?? opacity,
      speckleIntensity: newSpeckle ?? speckle,
      iridescence: newIrides ?? iridescence,
      textureScale: newTexScale ?? texScale,
      textureRotation: newTexRot ?? texRot,
      textureUrl: newTextureUrl ?? textureUrl,
    };

    onChange?.(updatedSettings);
  };

  // 处理颜色变化
  const handleColorChange = (newColor: string) => {
    setColor(newColor);
    updateSettings(newColor);
  };

  // 处理文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const url = URL.createObjectURL(file);
    setTextureUrl(url);
    updateSettings(undefined, undefined, undefined, undefined, url);
  };

  // 使用吸管工具
  const handleEyeDropper = async () => {
    if ('EyeDropper' in window) {
      try {
        // @ts-expect-error - EyeDropper API 可能不在所有TypeScript类型中
        const eyeDropper = new window.EyeDropper();
        const result = await eyeDropper.open();
        setColor(result.sRGBHex);
        updateSettings(result.sRGBHex);
      } catch (error) {
        console.error('EyeDropper error:', error);
      }
    } else {
      console.warn('EyeDropper API not supported');
    }
  };

  return (
    <div className="custom-material" data-layer="自定义材质">
      <div className="material-property-group column" data-layer="Frame 37">
        <div className="property-label" data-layer="颜色">颜色</div>
        <div className="color-picker-container">
          <HexColorPicker color={color} onChange={handleColorChange} />
          <Pipette 
            className="color-pipette-icon" 
            onClick={handleEyeDropper} 
            data-states="default" 
          />
        </div>
      </div>
      
      <div className="material-property-group" data-layer="Frame 36">
        <div className="property-label" data-layer="金属度">金属度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={metalness}
          onChange={(value) => {
            setMetalness(value);
            updateSettings(undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="speckle">
        <div className="property-label">斑点强度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={speckle}
          onChange={(value)=>{setSpeckle(value);updateSettings(undefined,undefined,undefined,undefined,undefined,value);}}
          showValue={false}
          width="100%"
        />
      </div>

      <div className="material-property-group" data-layer="iridescence">
        <div className="property-label">变色强度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={iridescence}
          onChange={(value)=>{setIridescence(value);updateSettings(undefined,undefined,undefined,undefined,undefined,undefined,value);}}
          showValue={false}
          width="100%"
        />
      </div>

      <div className="material-property-group" data-layer="texScale">
        <div className="property-label">纹理缩放</div>
        <Slider
          min={0.1}
          max={5}
          step={0.1}
          defaultValue={texScale}
          onChange={(value)=>{setTexScale(value);updateSettings(undefined,undefined,undefined,undefined,undefined,undefined,undefined,value);}}
          showValue={false}
          width="100%"
        />
      </div>

      <div className="material-property-group" data-layer="texRot">
        <div className="property-label">纹理旋转</div>
        <Slider
          min={0}
          max={360}
          step={1}
          defaultValue={texRot}
          onChange={(value)=>{setTexRot(value);updateSettings(undefined,undefined,undefined,undefined,undefined,undefined,undefined,undefined,value);}}
          showValue={false}
          width="100%"
        />
      </div>

      <div className="material-property-group" data-layer="Frame 34">
        <div className="property-label" data-layer="粗糙度">粗糙度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={roughness}
          onChange={(value) => {
            setRoughness(value);
            updateSettings(undefined, undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 35">
        <div className="property-label" data-layer="透明度">透明度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={opacity}
          onChange={(value) => {
            setOpacity(value);
            updateSettings(undefined, undefined, undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group column" data-layer="Frame 43">
        <div className="property-label" data-layer="纹理贴图">纹理贴图</div>
        <label className="texture-upload-area" data-layer="Frame 32">
          {textureUrl ? (
            <img src={textureUrl} alt="纹理贴图" className="texture-preview" />
          ) : (
            <>
              <div className="plus-icon" data-layer="plus">
                <Plus size={20} color="var(--color-content-regular)" />
              </div>
              <div className="upload-text" data-layer="上传图片">上传图片</div>
            </>
          )}
          <input 
            type="file" 
            accept="image/*" 
            onChange={handleFileUpload} 
            style={{ display: 'none' }} 
          />
        </label>
      </div>
    </div>
  );
};
