import { useState } from 'react';
import { InputBox } from '../../components/input-box/input-box';
import { PrimaryButton } from '../../components/primary-button/primary-button';
import './Login.css';
import { User, Lock } from 'lucide-react';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const handleLogin = () => {
    // 简单的验证逻辑，实际应用中应该与后端验证
    if (username === 'admin' && password === 'huitong-admin') {
      localStorage.setItem('isLoggedIn', 'true');
      window.location.href = '/admin/dashboard';
    } else {
      setError('用户名或密码错误');
    }
  };

  return (
    <div className="login-container theme-dark">
      <div className="login-card">
        <div className="login-header">
          <img className="login-logo" src="/images/Logo.png" alt="RINKO" />
          <h2>会通智能色彩云库 - 后台管理</h2>
        </div>
        
        <div className="login-form">
          <div className="input-group">
            <label>用户名</label>
            <InputBox 
              placeholder="请输入用户名" 
              value={username} 
              onChange={setUsername} 
              prefixIcon={<User size={18} />}
              fullWidth
            />
          </div>
          
          <div className="input-group">
            <label>密码</label>
            <InputBox 
              placeholder="请输入密码" 
              value={password} 
              onChange={setPassword}
              prefixIcon={<Lock size={18} />}
              type="password"
              fullWidth
            />
          </div>
          
          {error && <div className="login-error">{error}</div>}
          
          <PrimaryButton onClick={handleLogin} fullWidth>登录</PrimaryButton>
        </div>
      </div>
    </div>
  );
};

export default Login;
