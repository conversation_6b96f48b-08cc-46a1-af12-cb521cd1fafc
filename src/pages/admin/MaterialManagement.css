.material-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.management-toolbar {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 24px;
}

.toolbar-actions {
  display: flex;
  gap: 12px;
}

.materials-table-container {
  overflow-x: auto;
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-m);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.materials-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.materials-table th,
.materials-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  vertical-align: middle;
}

.materials-table th {
  font-weight: 500;
  color: var(--color-content-regular);
  background-color: var(--color-bg-primary);
  white-space: nowrap;
}

.materials-table tbody tr:hover {
  background-color: var(--color-bg-overlay);
}

.material-thumbnail {
  width: 60px;
  padding: 8px !important;
  vertical-align: middle;
}

.thumbnail-3d {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.thumbnail-3d canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
}

.material-name {
  font-weight: 500;
  color: var(--color-content-accent);
  min-width: 120px;
}

.material-color {
  width: 80px;
  padding: 8px 16px !important;
}

.color-preview {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: transform 0.2s;
}

.color-preview:hover {
  transform: scale(1.1);
}

.material-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
  padding: 8px 0 !important;
}

.material-date {
  white-space: nowrap;
  color: var(--color-content-mute);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .materials-table th,
  .materials-table td {
    padding: 10px 12px;
  }
  
  .material-thumbnail {
    width: 50px;
  }
  
  .thumbnail-preview {
    width: 36px;
    height: 36px;
  }
}

/* 材质编辑弹窗样式 */
.material-modal-container {
  width: 600px;
  max-width: 90%;
}

.material-preview {
  margin-bottom: 20px;
  padding: 24px;
  background-color: var(--color-bg-primary);
  border-radius: var(--radius-full);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-content-regular);
  margin-bottom: 20px;
  align-self: flex-start;
}

.preview-sphere {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  position: relative;
}

.material-settings {
  padding: 20px 0;
}

/* 通用模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-m);
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border);
}

.modal-header h2 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
}

.modal-form {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: var(--font-size-sm);
  color: var(--color-content-regular);
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  font-size: var(--font-size-sm);
  color: var(--color-content-accent);
  background-color: var(--color-bg-input);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-s);
  outline: none;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: var(--color-brand);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}
