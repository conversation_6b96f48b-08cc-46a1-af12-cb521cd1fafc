import { useState, useEffect } from 'react';
import { SearchBox } from '../../components/search-box/search-box';
import { PrimaryButton } from '../../components/primary-button/primary-button';
import { SecondaryButton } from '../../components/secondary-button/secondary-button';
import { IconButton } from '../../components/icon-button/icon-button';
import { Upload, Plus, Trash, Edit, Eye } from 'lucide-react';
import './ModelManagement.css';
import { apiService } from '../../services/api';
import type { ModelData } from '../../services/api';

// 使用从API服务导入的ModelData接口

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const ModelManagement = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedModel, setSelectedModel] = useState<ModelData | null>(null);
  const [loading, setLoading] = useState(false);
  const [models, setModels] = useState<ModelData[]>([
    // 初始状态为空，将通过API加载
  ]);

  // 加载模型列表
  useEffect(() => {
    const loadModels = async () => {
      setLoading(true);
      try {
        const data = await apiService.getModels();
        setModels(data);
      } catch (error) {
        console.error('加载模型失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadModels();
  }, []);

  const deleteModel = async (id: string) => {
    if (window.confirm('确定要删除这个模型吗？')) {
      try {
        await apiService.deleteModel(id);
        setModels(models.filter(model => model.id !== id));
      } catch (error) {
        console.error('删除模型失败:', error);
      }
    }
  };

  const filteredModels = models.filter(model => 
    model.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return <div className="loading-message">加载中...</div>;
  }

  return (
    <div className="model-management">
      <div className="management-toolbar">
        <SearchBox 
          placeholder="搜索模型" 
          value={searchQuery} 
          onChange={setSearchQuery}
          width={300}
        />
        
        <div className="toolbar-actions">
          <PrimaryButton icon={Plus} onClick={() => setShowAddModal(true)}>
            添加模型
          </PrimaryButton>
        </div>
      </div>
      
      <div className="models-table-container">
        <table className="models-table">
          <thead>
            <tr>
              <th>模型名称</th>
              <th>文件类型</th>
              <th>文件大小</th>
              <th>上传时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {filteredModels.map(model => (
              <tr key={model.id} className="model-row">
                <td className="model-name">{model.name}</td>
                <td className="file-type"><span className="file-type-badge">{model.fileType}</span></td>
                <td className="file-size">{model.size}</td>
                <td className="model-date">{formatDate(model.createdAt)}</td>
                <td className="model-actions">
                  <IconButton icon={Eye} size="small" onClick={() => alert('预览模型')} />
                  <IconButton icon={Edit} size="small" onClick={() => setSelectedModel(model)} />
                  <IconButton icon={Trash} size="small" onClick={() => deleteModel(model.id)} />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {showAddModal && (
        <ModelModal 
          onClose={() => setShowAddModal(false)} 
          onSave={async (name, modelFile, thumbnailFile) => {
            if (!modelFile) {
              alert('模型文件不能为空！');
              return;
            }
            try {
              const fileType = modelFile.name.split('.').pop()?.toUpperCase() || 'GLB';
              const size = `${(modelFile.size / (1024 * 1024)).toFixed(1)} MB`;
              const newModelData = await apiService.addModel(name, modelFile, thumbnailFile, fileType, size);
              if (newModelData) {
                setModels([...models, newModelData]);
              } else {
                console.error('添加模型失败: API未返回新模型数据');
                alert('添加模型失败，请检查控制台获取更多信息。');
              }
              setShowAddModal(false);
            } catch (error) {
              console.error('添加模型失败:', error);
              alert(`添加模型失败: ${error instanceof Error ? error.message : '未知错误'}`);
            }
          }}
        />
      )}
      
      {selectedModel && (
        <ModelModal 
          model={selectedModel}
          onClose={() => setSelectedModel(null)} 
          onSave={async (name, modelFile, thumbnailFile, existingModel) => {
            console.log('Attempted to update model (not implemented):', existingModel?.id, name, modelFile, thumbnailFile);
            alert('更新模型功能暂未实现。');
            setSelectedModel(null);
          }}
        />
      )}
    </div>
  );
};

interface ModelModalProps {
  model?: ModelData;
  onClose: () => void;
  onSave: (name: string, modelFile: File | null, thumbnailFile: File | null, existingModelData?: ModelData) => void | Promise<void>;
}

// 模型添加/编辑弹窗
const ModelModal = ({ model, onClose, onSave }: ModelModalProps) => {
  const [name, setName] = useState(model?.name || '');
  const [modelFile, setModelFile] = useState<File | null>(null);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!model && !modelFile) {
      alert('请选择一个模型文件。');
      return;
    }

    onSave(name, modelFile, thumbnailFile, model);
  };
  
  return (
    <div className="modal-overlay">
      <div className="modal-container">
        <div className="modal-header">
          <h2>{model ? '编辑模型' : '添加新模型'}</h2>
          <IconButton icon={Trash} onClick={onClose} size="small" />
        </div>
        
        <form className="modal-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label>模型名称</label>
            <input 
              type="text" 
              value={name} 
              onChange={(e) => setName(e.target.value)}
              placeholder="输入模型名称"
              required
              className="form-input"
            />
          </div>
          
          <div className="form-group">
            <label>上传模型文件</label>
            <div className="file-upload-area">
              <input 
                type="file" 
                id="model-file-input"
                onChange={(e) => setModelFile(e.target.files?.[0] || null)} 
                accept=".glb,.gltf,.obj,.fbx"
                className="file-input"
              />
              <SecondaryButton 
                icon={Upload} 
                onClick={() => document.getElementById('model-file-input')?.click()}
                type="button"
              >
                {modelFile ? modelFile.name : model ? '更换模型文件' : '选择模型文件'}
              </SecondaryButton>
              <span className="file-info">
                支持 .glb, .gltf, .obj, .fbx 格式
              </span>
            </div>
          </div>

          <div className="form-group">
            <label>上传缩略图 (可选)</label>
            <div className="file-upload-area">
              <input 
                type="file" 
                id="thumbnail-file-input"
                onChange={(e) => setThumbnailFile(e.target.files?.[0] || null)} 
                accept="image/png, image/jpeg, image/webp"
                className="file-input"
              />
              <SecondaryButton 
                icon={Upload} 
                onClick={() => document.getElementById('thumbnail-file-input')?.click()}
                type="button"
              >
                {thumbnailFile ? thumbnailFile.name : model?.thumbnail ? '更换缩略图' : '选择缩略图'}
              </SecondaryButton>
              <span className="file-info">
                支持 .png, .jpg, .webp 格式
              </span>
            </div>
          </div>
          
          <div className="modal-actions">
            <SecondaryButton onClick={onClose} type="button">
              取消
            </SecondaryButton>
            <PrimaryButton type="submit">
              {model ? '保存修改' : '添加模型'}
            </PrimaryButton>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ModelManagement;
