# 汇通材质渲染系统

一个基于 React + Three.js 的3D材质渲染和管理系统，支持实时材质预览、模型管理和材质库管理。

## 功能特性

- 🎨 实时3D材质渲染和预览
- 📦 3D模型文件管理（支持GLB/GLTF格式）
- 🎯 材质库管理和自定义材质创建
- 🖼️ 渲染图片导出和保存
- 🔧 后台管理系统
- 🌓 明暗主题切换

## 技术栈

- **前端**: React 19 + TypeScript + Vite
- **3D渲染**: Three.js + React Three Fiber + Drei
- **后端**: Node.js + Express + SQLite
- **样式**: CSS Variables + 组件化设计系统
- **图标**: Lucide React

## 开发环境

```bash
# 安装依赖
npm install

# 启动前端开发服务器
npm run dev

# 启动后端服务器
cd backend
npm start
```

## 项目结构

```
src/
├── components/          # 可复用组件
├── pages/              # 页面组件
├── services/           # API服务
├── styles/             # 样式系统
└── assets/             # 静态资源

backend/
├── server.js           # Express服务器
├── uploads/            # 文件上传目录
└── database.sqlite     # SQLite数据库
```
